You are tasked with implementing a high-performance, language-model-based lossless compressor designed to compete in the Hutter Prize. Follow the steps below with extreme precision. Each step has been engineered to comply with the Hutter Prize’s decompression runtime constraints and to maximize compression effectiveness via deep language modeling. Do not deviate.

TASK OBJECTIVE

Compress the enwik9 file using an LLM-based arithmetic coder. The final result must include a decompression program that:

Runs entirely on a single CPU core

Uses ≤10 GB RAM and ≤100 GB disk

Finishes decompression in ≤100 hours

Outputs byte-exact copy of enwik9

PHASE 1: MODEL TRAINING AND COMPRESSION (RUN ON GPU)

1. Data Preparation

Download and unzip enwik9 from: http://mattmahoney.net/dc/enwik9.zip

Load the raw text and remove trailing zeros or padding if present

Tokenize using the target LLM’s tokenizer (e.g., tokenizer.json from LLaMA or Mistral)

Segment token stream into 512-token chunks with overlaps to maintain context

2. LLM Selection and Configuration

Use meta-llama/Llama-2-7b-hf or mistralai/Mistral-7B-v0.1 from Hugging Face

Load model with float16 weights for fine-tuning

Set up LoRA adapters using peft (low-rank adapters)

Enable gradient checkpointing to fit model into GPU memory

3. Fine-Tuning

Fine-tune the model on the tokenized enwik9 using a causal language modeling loss

Use batch size ~4–8, sequence length = 512, and AdamW optimizer

Save LoRA adapter weights and config only (not full model)

Validate perplexity on held-out chunks

4. Quantization and Distillation (Optional)

If model is too large, distill it into a student network using token distribution KL-divergence

Apply 4-bit quantization using GPTQ or bitsandbytes

Validate that next-token predictions are accurate after quantization

5. Compression

For each 512-token chunk:

Use the model to get token probabilities for next token

Apply arithmetic coding using predicted distributions

Store compressed bitstream and chunk metadata

6. Output Archive Construction

Final compressed archive should include:

Quantized LLM weights

LoRA adapter weights (if used)

Tokenizer

Chunked compressed binaries

Chunk metadata (lengths, offsets)

Decompression script

PHASE 2: DECOMPRESSION (CPU-ONLY)

1. Environment Setup

Use ONNX Runtime (CPU only) or PyTorch with FBGEMM backend

Load model weights and LoRA adapter (if needed)

Load arithmetic decoder

2. Chunk Decoding

Decode each chunk sequentially using model predictions

For each token position, reconstruct the token using arithmetic decoder + model probability

Concatenate token sequence and detokenize to reconstruct byte-exact original text

OUTPUT

Archive with total size < 114MB (target)

Decompression runtime logs proving compliance with Hutter Prize constraints

Byte-exact reproduction of original enwik9